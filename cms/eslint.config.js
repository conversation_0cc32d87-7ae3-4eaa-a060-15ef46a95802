import prettier from 'eslint-config-prettier';
import svelte from 'eslint-plugin-svelte';
import svelteParser from "svelte-eslint-parser";
import tsParser from "@typescript-eslint/parser";

export default [{
    files: ["**/*.svelte"], // Apply this configuration to .svelte files
    languageOptions: {
        parser: svelteParser,
        parserOptions: {
            parser: tsParser, // Specify TypeScript parser for script tags within Svelte files
            extraFileExtensions: [".svelte"], // Important for Svelte files
        },
    },
}, prettier, ...svelte.configs.prettier];
