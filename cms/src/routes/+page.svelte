<script lang="ts">
	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';
	import { getAuthStore } from '$lib/stores/auth.svelte.js';

	// Get auth store
	const authStore = getAuthStore();

	onMount(() => {
		// Redirect based on authentication status
		if (authStore.isAuthenticated) {
			goto('/reviews');
		} else {
			goto('/login');
		}
	});
</script>

<div class="flex min-h-svh items-center justify-center">
	<div class="text-center">
		<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
		<p class="text-muted-foreground">Redirecting...</p>
	</div>
</div>
