<script lang="ts">
	import '../app.css';

	import { setAuthStore } from '$lib/stores/auth.svelte.js';
	import { setNavigationStore } from '$lib/stores/navigation.svelte.js';
	import ListDetailsIcon from '@tabler/icons-svelte/icons/list-details';

	let { children } = $props();

	// Initialize auth store first
	setAuthStore();

	// Initialize navigation store without hardcoded user
	setNavigationStore({
		headerTitle: 'Dashboard',
		currentRoute: '/dashboard',
		items: [
			{
				id: 'reviews',
				title: 'Reviews',
				url: '/reviews',
				icon: ListDetailsIcon,
				badge: 3
			}
		]
	});
</script>

{@render children()}
