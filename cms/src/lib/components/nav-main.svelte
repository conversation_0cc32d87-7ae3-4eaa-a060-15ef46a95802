<script lang="ts">
	import { goto } from '$app/navigation';
	import * as Sidebar from '$lib/components/ui/sidebar/index.js';
	import { getNavigationStore } from '$lib/stores/navigation.svelte.js';
	import type { Icon } from '@tabler/icons-svelte';

	let { items }: { items: { title: string; url: string; icon?: Icon }[] } = $props();

	// Get navigation store to check active state
	const navigationStore = getNavigationStore();
</script>

<Sidebar.Group>
	<Sidebar.GroupContent class="flex flex-col gap-2">
		<Sidebar.Menu>
			{#each items as item (item.title)}
				<Sidebar.MenuItem>
					<Sidebar.MenuButton
						tooltipContent={item.title}
						onclick={() => goto(item.url)}
						isActive={navigationStore.currentRoute === item.url || navigationStore.currentRoute.startsWith(item.url + '/')}
					>
						{#if item.icon}
							<item.icon />
						{/if}
						<span>{item.title}</span>
					</Sidebar.MenuButton>
				</Sidebar.MenuItem>
			{/each}
		</Sidebar.Menu>
	</Sidebar.GroupContent>
</Sidebar.Group>
