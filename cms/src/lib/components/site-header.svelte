<script lang="ts">
	import { Separator } from '$lib/components/ui/separator/index.js';
	import * as Sidebar from '$lib/components/ui/sidebar/index.js';
	import { getNavigationStore } from '$lib/stores/navigation.svelte.js';

	// Get navigation store from context
	const navigationStore = getNavigationStore();
</script>

<header
	class="flex h-(--header-height) shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-(--header-height)"
>
	<div class="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
		<Sidebar.Trigger class="-ml-1" />
		<Separator orientation="vertical" class="mx-2 data-[orientation=vertical]:h-4" />
		<h1 class="text-base font-medium">{navigationStore.headerTitle}</h1>

		<!-- Header Actions -->
		{#if navigationStore.headerActions.length > 0}
			<div class="ml-auto flex items-center gap-2">
				{#each navigationStore.headerActions as action (action.id)}
					{#if action.isVisible !== false}
						<button
							type="button"
							class="inline-flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground"
							onclick={action.onClick}
							title={action.title}
						>
							{#if action.icon}
								<action.icon class="h-4 w-4" />
							{/if}
							{action.title}
						</button>
					{/if}
				{/each}
			</div>
		{/if}
	</div>
</header>
