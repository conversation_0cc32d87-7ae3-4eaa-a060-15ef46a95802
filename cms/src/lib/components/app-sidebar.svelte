<script lang="ts">
	import * as Sidebar from '$lib/components/ui/sidebar/index.js';
	import InnerShadowTopIcon from '@tabler/icons-svelte/icons/inner-shadow-top';
	import type { ComponentProps } from 'svelte';
	import NavMain from './nav-main.svelte';
	import NavUser from './nav-user.svelte';
	import { getNavigationStore } from '$lib/stores/navigation.svelte.js';

	// Get navigation store from context
	const navigationStore = getNavigationStore();

	let { ...restProps }: ComponentProps<typeof Sidebar.Root> = $props();
</script>

<Sidebar.Root collapsible="offcanvas" {...restProps}>
	<Sidebar.Header>
		<Sidebar.Menu>
			<Sidebar.MenuItem>
				<Sidebar.MenuButton class="data-[slot=sidebar-menu-button]:!p-1.5">
					{#snippet child({ props })}
						<a href="##" {...props}>
							<InnerShadowTopIcon class="!size-5" />
							<span class="text-base font-semibold">URA</span>
						</a>
					{/snippet}
				</Sidebar.MenuButton>
			</Sidebar.MenuItem>
		</Sidebar.Menu>
	</Sidebar.Header>
	<Sidebar.Content>
		<NavMain items={navigationStore.visibleItems} />
	</Sidebar.Content>
	<Sidebar.Footer>
		{#if navigationStore.user}
			<NavUser user={navigationStore.user} />
		{/if}
	</Sidebar.Footer>
</Sidebar.Root>
