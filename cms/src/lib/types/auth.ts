/**
 * User roles enum for consistent role management across the application
 */
export enum UserRole {
	ADMIN = 'admin',
	MODERATOR = 'moderator',
	SITE_ADMIN = 'site-admin',
	EDITOR = 'editor',
	USER = 'user'
}

/**
 * Helper functions for role management
 */
export class RoleHelper {
	/**
	 * Get all available roles
	 */
	static getAllRoles(): UserRole[] {
		return Object.values(UserRole);
	}

	/**
	 * Check if a role is valid
	 */
	static isValidRole(role: string): role is UserRole {
		return Object.values(UserRole).includes(role as UserRole);
	}

	/**
	 * Get role display name
	 */
	static getRoleDisplayName(role: UserRole): string {
		switch (role) {
			case UserRole.ADMIN:
				return 'Administrator';
			case UserRole.MODERATOR:
				return 'Moderator';
			case UserRole.SITE_ADMIN:
				return 'Site Administrator';
			case UserRole.EDITOR:
				return 'Editor';
			case UserRole.USER:
				return 'User';
			default:
				return role;
		}
	}

	/**
	 * Get roles with admin privileges
	 */
	static getAdminRoles(): UserRole[] {
		return [UserRole.ADMIN, UserRole.SITE_ADMIN];
	}

	/**
	 * Get roles with moderation privileges
	 */
	static getModerationRoles(): UserRole[] {
		return [UserRole.ADMIN, UserRole.SITE_ADMIN, UserRole.MODERATOR];
	}

	/**
	 * Get roles with editing privileges
	 */
	static getEditingRoles(): UserRole[] {
		return [UserRole.ADMIN, UserRole.SITE_ADMIN, UserRole.MODERATOR, UserRole.EDITOR];
	}
}
