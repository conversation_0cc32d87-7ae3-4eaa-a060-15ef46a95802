import { goto } from '$app/navigation';
import { getAuthStore } from '$lib/stores/auth.svelte.js';
import { UserRole } from '$lib/types/auth';

/**
 * Auth guard hook that protects routes from unauthorized access
 * Usage: Call this in components that require authentication
 */
export function useAuthGuard(
	options: {
		redirectTo?: string;
		requiredRoles?: UserRole[];
	} = {}
) {
	const authStore = getAuthStore();
	const { redirectTo = '/login', requiredRoles = [] } = options;

	// Check authentication and roles
	$effect(() => {
		// Wait for auth to finish loading
		if (authStore.isLoading) return;

		// Check if user is authenticated
		if (!authStore.isAuthenticated) {
			goto(redirectTo);
			return;
		}

		// Check required roles - user must have at least one of the required roles
		if (requiredRoles.length > 0) {
			if (!authStore.hasAnyRole(requiredRoles)) {
				goto('/unauthorized');
				return;
			}
		}
	});

	return {
		isAuthenticated: authStore.isAuthenticated,
		isLoading: authStore.isLoading,
		user: authStore.user
	};
}

/**
 * Redirect authenticated users away from auth pages (like login)
 * Usage: Call this in login/register pages
 */
export function useGuestGuard(redirectTo: string = '/reviews') {
	const authStore = getAuthStore();

	$effect(() => {
		if (!authStore.isLoading && authStore.isAuthenticated) {
			goto(redirectTo);
		}
	});

	return {
		isAuthenticated: authStore.isAuthenticated,
		isLoading: authStore.isLoading
	};
}

/**
 * Role-based guard for specific features
 * Can check for a single role or multiple roles
 */
export function useRoleGuard(roles: UserRole | UserRole[]) {
	const authStore = getAuthStore();
	const roleArray = Array.isArray(roles) ? roles : [roles];

	const hasRole = $derived(
		roleArray.length === 1
			? authStore.hasRole(roleArray[0])
			: authStore.hasAnyRole(roleArray)
	);

	return {
		get hasRole() { return hasRole; },
		get isAuthenticated() { return authStore.isAuthenticated; },
		get isLoading() { return authStore.isLoading; }
	};
}
