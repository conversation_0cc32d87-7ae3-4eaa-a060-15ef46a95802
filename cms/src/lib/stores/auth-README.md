# Authentication System

A comprehensive authentication system for Svelte 5 applications with route guards, permission management, and seamless integration with the navigation store.

## Features

- 🔐 **Complete Auth Flow**: Login, logout, and session management
- 🛡️ **Route Guards**: Protect routes with authentication and permission checks
- 👤 **User Management**: User profile, roles, and permissions
- 🔄 **Persistent Sessions**: Automatic session restoration from localStorage
- 🚀 **Svelte 5 Runes**: Built with modern Svelte 5 reactivity
- 🎯 **TypeScript**: Full type safety throughout
- 🔒 **Permission System**: Role-based and permission-based access control

## Quick Start

### 1. Initialize Auth Store

```typescript
// In your root layout or main component
import { setAuthStore } from '$lib/stores/auth.svelte.js';

const authStore = setAuthStore();
```

### 2. Protect Routes with Auth Guard

```typescript
// In protected route components
import { useAuthGuard } from '$lib/hooks/auth-guard.svelte.js';

const authGuard = useAuthGuard();

// Optional: Require specific roles
import { UserRole } from '$lib/types/auth';

const authGuard = useAuthGuard({
  requiredRoles: [UserRole.ADMIN, UserRole.MODERATOR]
});
```

### 3. Redirect Authenticated Users from Auth Pages

```typescript
// In login/register pages
import { useGuestGuard } from '$lib/hooks/auth-guard.svelte.js';

const guestGuard = useGuestGuard('/reviews');
```

## Authentication Store API

### State Properties

- `user` - Current user object or null
- `isAuthenticated` - Boolean indicating auth status
- `isLoading` - Boolean indicating loading state
- `error` - Current error message or null

### Methods

#### Authentication
- `login(email, password)` - Authenticate user
- `logout()` - Sign out and redirect to login
- `refresh()` - Validate and refresh session

#### User Management
- `updateUser(updates)` - Update user information
- `hasRole(role)` - Check if user has specific role
- `hasAnyRole(roles)` - Check if user has any of the specified roles

#### Error Handling
- `setError(message)` - Set error message
- `clearError()` - Clear error message

## Auth Guards

### `useAuthGuard(options)`

Protects routes from unauthorized access.

```typescript
import { UserRole } from '$lib/types/auth';

const authGuard = useAuthGuard({
  redirectTo: '/login',                                    // Where to redirect if not authenticated
  requiredRoles: [UserRole.ADMIN, UserRole.MODERATOR]     // Required roles (user needs at least one)
});
```

### `useGuestGuard(redirectTo)`

Redirects authenticated users away from auth pages.

```typescript
const guestGuard = useGuestGuard('/dashboard');
```

### `useRoleGuard(roles)`

Check specific roles for UI elements. Can accept a single role or array of roles.

```typescript
import { UserRole } from '$lib/types/auth';

// Single role
const roleGuard = useRoleGuard(UserRole.ADMIN);

// Multiple roles (user needs at least one)
const roleGuard = useRoleGuard([UserRole.ADMIN, UserRole.MODERATOR, UserRole.SITE_ADMIN]);

// In template
{#if roleGuard.hasRole}
  <AdminSettings />
{/if}
```

## Integration Examples

### Protected Route Component

```typescript
<script lang="ts">
  import { useAuthGuard } from '$lib/hooks/auth-guard.svelte.js';
  
  const authGuard = useAuthGuard({
    requiredRoles: [UserRole.ADMIN]
  });
</script>

{#if authGuard.isLoading}
  <div>Loading...</div>
{:else if authGuard.isAuthenticated}
  <main>
    <!-- Protected content -->
  </main>
{/if}
```

### Login Form Integration

```typescript
<script lang="ts">
  import { getAuthStore } from '$lib/stores/auth.svelte.js';
  
  const authStore = getAuthStore();
  
  let email = $state('');
  let password = $state('');
  
  async function handleLogin(event) {
    event.preventDefault();
    const success = await authStore.login(email, password);
    if (success) {
      goto('/dashboard');
    }
  }
</script>

<form onsubmit={handleLogin}>
  {#if authStore.error}
    <div class="error">{authStore.error}</div>
  {/if}
  
  <input bind:value={email} type="email" required />
  <input bind:value={password} type="password" required />
  
  <button type="submit" disabled={authStore.isLoading}>
    {authStore.isLoading ? 'Signing in...' : 'Login'}
  </button>
</form>
```

### Navigation Integration

```typescript
<script lang="ts">
  import { getAuthStore } from '$lib/stores/auth.svelte.js';
  import { setNavigationStore } from '$lib/stores/navigation.svelte.js';
  
  const authStore = getAuthStore();
  
  const navigationStore = setNavigationStore({
    user: authStore.user ? {
      name: authStore.user.name,
      email: authStore.user.email,
      avatar: authStore.user.avatar
    } : undefined
  });
</script>
```

## Default Credentials

For development/testing purposes:

- **Email**: `<EMAIL>`
- **Password**: `password`

## Customization

### Custom Authentication Logic

Replace the mock authentication in `auth.svelte.ts`:

```typescript
async login(email: string, password: string): Promise<boolean> {
  this._isLoading = true;
  this._error = null;

  try {
    // Replace with your API call
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password })
    });

    if (response.ok) {
      const { user, token } = await response.json();
      
      this._user = user;
      localStorage.setItem('auth_user', JSON.stringify(user));
      localStorage.setItem('auth_token', token);
      
      return true;
    } else {
      const { message } = await response.json();
      this._error = message;
      return false;
    }
  } catch (error) {
    this._error = 'Login failed';
    return false;
  } finally {
    this._isLoading = false;
  }
}
```

### Custom User Type

Extend the User interface in `auth.svelte.ts`:

```typescript
export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role?: string;
  permissions?: string[];
  // Add your custom fields
  department?: string;
  lastLogin?: Date;
}
```

## Security Considerations

1. **Token Storage**: Currently uses localStorage - consider httpOnly cookies for production
2. **Token Validation**: Implement server-side token validation
3. **CSRF Protection**: Add CSRF tokens for state-changing operations
4. **Session Timeout**: Implement automatic session expiration
5. **Permission Validation**: Always validate permissions server-side

## File Structure

```
src/lib/
├── stores/
│   ├── auth.svelte.ts           # Main auth store
│   └── auth-README.md           # This documentation
├── hooks/
│   └── auth-guard.svelte.ts     # Auth guard hooks
└── components/
    ├── login-form.svelte        # Login form component
    └── nav-user.svelte          # User navigation component

src/routes/
├── login/
│   └── +page.svelte            # Login page
├── unauthorized/
│   └── +page.svelte            # Unauthorized access page
└── +page.svelte                # Root redirect page
```

## Best Practices

1. **Initialize Early**: Set up auth store in root layout
2. **Use Guards**: Always use auth guards for protected routes
3. **Handle Loading**: Show loading states during auth operations
4. **Error Handling**: Display meaningful error messages
5. **Permissions**: Check permissions both client and server-side
6. **Logout**: Provide clear logout functionality
7. **Session Management**: Handle session expiration gracefully

## Troubleshooting

### Common Issues

1. **Store not found**: Ensure `setAuthStore()` is called in parent component
2. **Infinite redirects**: Check guard logic and redirect paths
3. **Permissions not working**: Verify user permissions are set correctly
4. **Session not persisting**: Check localStorage availability and browser settings

### Debug Utilities

```typescript
// Check auth state
console.log('Auth State:', {
  isAuthenticated: authStore.isAuthenticated,
  user: authStore.user,
  isLoading: authStore.isLoading,
  error: authStore.error
});

// Check roles
console.log('Has admin role:', authStore.hasRole(UserRole.ADMIN));
console.log('Has any admin roles:', authStore.hasAnyRole([UserRole.ADMIN, UserRole.SITE_ADMIN]));
```
