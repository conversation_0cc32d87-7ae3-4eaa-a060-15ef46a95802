# Navigation Store Usage Examples

This document provides examples of how to use the Svelte 5 Navigation Store.

## Basic Setup

### 1. Initialize the Navigation Store

```typescript
// In your main layout or page component
import { setNavigationStore } from '$lib/stores/navigation.svelte.js';
import HomeIcon from '@tabler/icons-svelte/icons/home';
import SettingsIcon from '@tabler/icons-svelte/icons/settings';

const navigationStore = setNavigationStore({
  headerTitle: 'My App',
  currentRoute: '/dashboard',
  items: [
    {
      id: 'home',
      title: 'Home',
      url: '/',
      icon: HomeIcon
    },
    {
      id: 'settings',
      title: 'Settings',
      url: '/settings',
      icon: SettingsIcon,
      badge: 2
    }
  ],
  user: {
    name: '<PERSON>',
    email: '<EMAIL>'
  }
});
```

### 2. Use the Navigation Store in Components

```typescript
// In any child component
import { getNavigationStore } from '$lib/stores/navigation.svelte.js';

const navigationStore = getNavigationStore();

// Access reactive values
console.log(navigationStore.headerTitle); // "My App"
console.log(navigationStore.visibleItems); // Array of navigation items
console.log(navigationStore.activeItem); // Currently active item
```

## Advanced Usage

### Dynamic Navigation Management

```typescript
// Add a new navigation item
navigationStore.addItem({
  id: 'reports',
  title: 'Reports',
  url: '/reports',
  icon: ReportsIcon,
  badge: 5
});

// Update an existing item
navigationStore.updateItem('settings', {
  badge: 3,
  title: 'Settings & Preferences'
});

// Remove an item
navigationStore.removeItem('reports');

// Add nested navigation
navigationStore.addItem({
  id: 'admin-users',
  title: 'Users',
  url: '/admin/users',
  icon: UsersIcon
}, 'admin'); // parent ID
```

### Header Management

```typescript
// Update header title
navigationStore.setHeaderTitle('Dashboard Overview');

// Set breadcrumbs
navigationStore.setBreadcrumbs([
  { title: 'Home', url: '/' },
  { title: 'Admin', url: '/admin' },
  { title: 'Users' }
]);

// Add header actions
navigationStore.addHeaderAction({
  id: 'export',
  title: 'Export Data',
  icon: ExportIcon,
  onClick: () => {
    // Handle export
  }
});
```

### Route Management

```typescript
// Update current route (typically done automatically by router)
navigationStore.setCurrentRoute('/settings');

// Check if item is active
const isActive = navigationStore.isItemActive(item);

// Get active item
const activeItem = navigationStore.activeItem;
```

### User Management

```typescript
// Set user info
navigationStore.setUser({
  name: 'Jane Smith',
  email: '<EMAIL>',
  avatar: '/avatars/jane.jpg'
});
```

## Integration with SvelteKit

### Automatic Route Updates

```typescript
// In your +layout.svelte
import { page } from '$app/stores';
import { getNavigationStore } from '$lib/stores/navigation.svelte.js';

const navigationStore = getNavigationStore();

// Update current route when page changes
$effect(() => {
  navigationStore.setCurrentRoute($page.url.pathname);
});
```

### Dynamic Title Updates

```typescript
// In individual page components
import { getNavigationStore } from '$lib/stores/navigation.svelte.js';

const navigationStore = getNavigationStore();

// Update title when component mounts
$effect(() => {
  navigationStore.setHeaderTitle('Specific Page Title');
  
  // Cleanup on unmount
  return () => {
    navigationStore.setHeaderTitle('Default Title');
  };
});
```

## Permissions and Visibility

```typescript
// Set user permissions
const userPermissions = ['read', 'write', 'admin'];

// Check if user has permission for item
const hasPermission = navigationStore.hasPermission(item, userPermissions);

// Filter items by permissions
const allowedItems = navigationStore.visibleItems.filter(item => 
  navigationStore.hasPermission(item, userPermissions)
);

// Hide/show items dynamically
navigationStore.updateItem('admin-panel', {
  isVisible: user.role === 'admin'
});
```

## Best Practices

1. **Initialize Early**: Set up the navigation store in your main layout component
2. **Use Context**: Always use `getNavigationStore()` in child components
3. **Reactive Updates**: Leverage Svelte 5 runes for automatic reactivity
4. **Type Safety**: Use TypeScript interfaces for better development experience
5. **Cleanup**: Remove temporary items and actions when components unmount
6. **Performance**: Use derived values for computed navigation states

## Common Patterns

### Conditional Navigation

```typescript
// Show different navigation based on user role
$effect(() => {
  if (user.role === 'admin') {
    navigationStore.addItem({
      id: 'admin',
      title: 'Admin Panel',
      url: '/admin',
      icon: AdminIcon
    });
  } else {
    navigationStore.removeItem('admin');
  }
});
```

### Badge Updates

```typescript
// Update badges based on data
$effect(() => {
  const unreadCount = notifications.filter(n => !n.read).length;
  navigationStore.updateItem('notifications', {
    badge: unreadCount > 0 ? unreadCount : undefined
  });
});
```

### Multi-level Navigation

```typescript
const navigationItems = [
  {
    id: 'dashboard',
    title: 'Dashboard',
    url: '/dashboard',
    icon: DashboardIcon
  },
  {
    id: 'admin',
    title: 'Administration',
    url: '/admin',
    icon: AdminIcon,
    children: [
      {
        id: 'admin-users',
        title: 'Users',
        url: '/admin/users',
        icon: UsersIcon
      },
      {
        id: 'admin-settings',
        title: 'Settings',
        url: '/admin/settings',
        icon: SettingsIcon
      }
    ]
  }
];

navigationStore.setItems(navigationItems);
```
