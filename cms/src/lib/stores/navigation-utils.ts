import type { NavigationItem, NavigationStore } from './navigation.svelte.js';

/**
 * Utility functions for navigation store integration
 */

/**
 * Auto-sync navigation store with SvelteKit page store
 * Call this in your root layout component
 */
export function syncNavigationWithPage(
	navigationStore: NavigationStore,
	page: { url: { pathname: string } }
) {
	navigationStore.setCurrentRoute(page.url.pathname);
	
	// Auto-update header title based on active item
	const activeItem = navigationStore.activeItem;
	if (activeItem) {
		navigationStore.setHeaderTitle(activeItem.title);
	}
}

/**
 * Generate breadcrumbs from navigation hierarchy
 */
export function generateBreadcrumbs(
	items: NavigationItem[],
	currentPath: string
): Array<{ title: string; url?: string }> {
	const breadcrumbs: Array<{ title: string; url?: string }> = [];
	
	function findPath(items: NavigationItem[], path: string, parents: NavigationItem[] = []): boolean {
		for (const item of items) {
			const currentParents = [...parents, item];
			
			if (item.url === path) {
				// Found the target, build breadcrumbs
				breadcrumbs.push({ title: 'Home', url: '/' });
				for (const parent of currentParents) {
					breadcrumbs.push({
						title: parent.title,
						url: parent === item ? undefined : parent.url
					});
				}
				return true;
			}
			
			if (item.children && findPath(item.children, path, currentParents)) {
				return true;
			}
		}
		return false;
	}
	
	findPath(items, currentPath);
	return breadcrumbs;
}

/**
 * Filter navigation items by user permissions
 */
export function filterItemsByPermissions(
	items: NavigationItem[],
	userPermissions: string[]
): NavigationItem[] {
	return items
		.filter(item => {
			if (!item.permissions) return true;
			return item.permissions.some(permission => userPermissions.includes(permission));
		})
		.map(item => ({
			...item,
			children: item.children 
				? filterItemsByPermissions(item.children, userPermissions)
				: undefined
		}));
}

/**
 * Find navigation item by URL
 */
export function findItemByUrl(items: NavigationItem[], url: string): NavigationItem | undefined {
	for (const item of items) {
		if (item.url === url) {
			return item;
		}
		if (item.children) {
			const found = findItemByUrl(item.children, url);
			if (found) return found;
		}
	}
	return undefined;
}

/**
 * Get all parent items for a given item
 */
export function getParentItems(items: NavigationItem[], targetId: string): NavigationItem[] {
	const parents: NavigationItem[] = [];
	
	function findParents(items: NavigationItem[], targetId: string, currentParents: NavigationItem[] = []): boolean {
		for (const item of items) {
			if (item.id === targetId) {
				parents.push(...currentParents);
				return true;
			}
			
			if (item.children && findParents(item.children, targetId, [...currentParents, item])) {
				return true;
			}
		}
		return false;
	}
	
	findParents(items, targetId);
	return parents;
}

/**
 * Create a navigation item with default values
 */
export function createNavigationItem(
	id: string,
	title: string,
	url: string,
	options: Partial<NavigationItem> = {}
): NavigationItem {
	return {
		id,
		title,
		url,
		isVisible: true,
		...options
	};
}

/**
 * Validate navigation item structure
 */
export function validateNavigationItem(item: NavigationItem): string[] {
	const errors: string[] = [];
	
	if (!item.id) errors.push('Navigation item must have an id');
	if (!item.title) errors.push('Navigation item must have a title');
	if (!item.url) errors.push('Navigation item must have a url');
	
	if (item.children) {
		for (const child of item.children) {
			errors.push(...validateNavigationItem(child));
		}
	}
	
	return errors;
}

/**
 * Sort navigation items by a given property
 */
export function sortNavigationItems(
	items: NavigationItem[],
	sortBy: keyof NavigationItem = 'title',
	direction: 'asc' | 'desc' = 'asc'
): NavigationItem[] {
	return [...items].sort((a, b) => {
		const aValue = a[sortBy];
		const bValue = b[sortBy];
		
		if (aValue === undefined && bValue === undefined) return 0;
		if (aValue === undefined) return direction === 'asc' ? 1 : -1;
		if (bValue === undefined) return direction === 'asc' ? -1 : 1;
		
		if (aValue < bValue) return direction === 'asc' ? -1 : 1;
		if (aValue > bValue) return direction === 'asc' ? 1 : -1;
		return 0;
	});
}

/**
 * Get navigation statistics
 */
export function getNavigationStats(items: NavigationItem[]) {
	let totalItems = 0;
	let visibleItems = 0;
	let itemsWithBadges = 0;
	let maxDepth = 0;
	
	function analyze(items: NavigationItem[], depth = 0) {
		maxDepth = Math.max(maxDepth, depth);
		
		for (const item of items) {
			totalItems++;
			if (item.isVisible !== false) visibleItems++;
			if (item.badge !== undefined) itemsWithBadges++;
			
			if (item.children) {
				analyze(item.children, depth + 1);
			}
		}
	}
	
	analyze(items);
	
	return {
		totalItems,
		visibleItems,
		itemsWithBadges,
		maxDepth
	};
}

/**
 * Export navigation structure to JSON
 */
export function exportNavigationStructure(items: NavigationItem[]): string {
	// Remove functions and non-serializable properties
	const cleanItems = items.map(item => ({
		...item,
		icon: item.icon ? '[Icon Component]' : undefined,
		children: item.children ? item.children.map(child => ({
			...child,
			icon: child.icon ? '[Icon Component]' : undefined
		})) : undefined
	}));
	
	return JSON.stringify(cleanItems, null, 2);
}
